{* 
  Reusable UI menu component
  
  @param items        Array of menu items, each containing:
                      - url: URL to navigate to
                      - title: Menu item title/text or translation key name
                      - active: <PERSON><PERSON>an indicating if item is selected
                      - condition: (optional) Condition to show this item
                      - translate: (optional) <PERSON><PERSON><PERSON> indicating if title is a translation key
                      - id: (optional) ID for the menu item
                      - avatar: (optional) URL to avatar image
                      - additionalInfo: (optional) Additional info text/description
                      - isRich: (optional) Boolean indicating if this is a rich item with avatar and additional info
                      - isSeparator: (optional) Boolean indicating if this is a separator
                      - extraItem: (optional) Object containing:
                        - url: URL for the extra item
                        - class: CSS class for the extra item
                        - content: HTML content for the extra item
                        - condition: (optional) Condition to show this item
                        - onclick: (optional) onclick handler
  @param id           (optional) ID for the menu element
  @param additionalCssClass (optional) Additional CSS classes
  @param ownblockData (optional) Object containing:
                      - url: URL to navigate back to
                      - name: Name to display
                      - img: Image URL
                      - extraItem: (optional) Object containing:
                        - url: URL for the extra item
                        - class: CSS class for the extra item
                        - content: HTML content for the extra item
                        - condition: (optional) Condition to show this item
                        - onclick: (optional) onclick handler
                      Note: If not provided, will be auto-generated based on $wallOwner. Will be auto-generated based on $user or $club if $ownblock is true
*}
<div class="page_block tabs ui_rmenu ui_rmenu_pr {$additionalCssClass ?? ''}" id="{$id ?? 'wall_rmenu'}" role="list">
    {var $owner = isset($ownblock) && $ownblock ? (isset($club) ? $club : $user) : $wallOwner}
    {if isset($owner)}
        {if $owner instanceof \openvk\Web\Models\Entities\Club}
            {var $ownblockData = [
                'url' => "/club" . $owner->getId(),
                'name' => $owner->getName(),
                'img' => $owner->getAvatarURL()
            ]}
        {else}
            {var $ownblockData = [
                'url' => "/id" . $owner->getId(),
                'name' => $owner->getFirstName() . " " . $owner->getLastName(),
                'img' => $owner->getAvatarURL()
            ]}
        {/if}
    {/if}

    {if isset($ownblockData)}
        <a class="ui_ownblock clear_fix" href="{$ownblockData['url']}">
            <img class="ui_ownblock_img" src="{$ownblockData['img']}" n:if="isset($ownblockData['img'])">
            <div class="ui_ownblock_info">
                {if isset($ownblockData['extraItem']) && (!isset($ownblockData['extraItem']['condition']) || $ownblockData['extraItem']['condition'])}
                    <span class="ui_rmenu_extra_item {$ownblockData['extraItem']['class']}"
                          data-href="{$ownblockData['extraItem']['url']}"
                          n:attr="onclick => !isset($ownblockData['extraItem']['onclick']) ? 'event.stopPropagation(); window.location.href=this.dataset.href;' : $ownblockData['extraItem']['onclick']">
                        {$ownblockData['extraItem']['content']|noescape}
                    </span>
                {/if}
                <div class="ui_ownblock_label">{$ownblockData['name']}</div>
                <div class="ui_ownblock_hint"><vkifyloc name="back_to_page"></vkifyloc></div>
            </div>
        </a>
        <div class="ui_rmenu_sep"></div>
    {/if}
    {foreach $items as $item}
        {if !isset($item['condition']) || $item['condition']}
            {if isset($item['isSeparator']) && $item['isSeparator']}
                <div class="ui_rmenu_sep"></div>
            {else}
                <div class="ui_rmenu_item_wrap">
                    <a class="ui_rmenu_item {$item['active'] ? 'ui_rmenu_item_sel' : ''} {$item['isRich'] ? 'ui_ownblock' : ''}" {if !$item['active']}href="{$item['url']}"{/if} id="{$item['id']}">
                        {if $item['isRich']}
                            <img class="ui_ownblock_img" src="{$item['avatar']}" />
                            <div class="ui_ownblock_info">
                                {if isset($item['extraItem']) && (!isset($item['extraItem']['condition']) || $item['extraItem']['condition'])}
                                    <span class="ui_rmenu_extra_item {$item['extraItem']['class']}"
                                          data-href="{$item['extraItem']['url']}"
                                          n:attr="onclick => !isset($item['extraItem']['onclick']) ? 'event.stopPropagation(); window.location.href=this.dataset.href;' : $item['extraItem']['onclick']">
                                        {$item['extraItem']['content']|noescape}
                                    </span>
                                {/if}
                                <div class="ui_ownblock_label">{if isset($item['translate']) && $item['translate'] === false}{$item['title']}{else}{_$item['title']}{/if}</div>
                                <div class="ui_ownblock_hint">{$item['additionalInfo']}</div>
                            </div>
                        {else}
                            <span>
                                {if isset($item['extraItem']) && (!isset($item['extraItem']['condition']) || $item['extraItem']['condition'])}
                                    <span class="ui_rmenu_extra_item {$item['extraItem']['class']}"
                                          data-href="{$item['extraItem']['url']}"
                                          n:attr="onclick => !isset($item['extraItem']['onclick']) ? 'event.stopPropagation(); window.location.href=this.dataset.href;' : $item['extraItem']['onclick']">
                                        {$item['extraItem']['content']|noescape}
                                    </span>
                                {/if}
                                {if isset($item['translate']) && $item['translate'] === false}{$item['title']}{else}{_$item['title']}{/if}
                            </span>
                        {/if}
                    </a>
                </div>
            {/if}
        {/if}
    {/foreach}
</div> 