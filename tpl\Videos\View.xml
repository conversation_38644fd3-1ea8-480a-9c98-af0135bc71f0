{extends "../@layout.xml"}

{block title}{_video}{/block}

{block content}
    {include "../components/page_crumb_header.xml", crumbs: [
        ['href' => $user->getURL(), 'title' => $user->getCanonicalName()],
        ['href' => "/videos{$user->getId()}", 'title' => tr("videos")],
        ['title' => $video->getName()]
    ]}
    <div class='page_block'>
        <div class="video_block_layout" style="padding-top: 20px;">
            {if $video->getType() === 0}
                <div class="bsdn" data-name="{$video->getName()}" data-author="{$user->getCanonicalName()}">
                    <video src="{$video->getURL()}"></video>
                </div>
            {else}
                {var $driver = $video->getVideoDriver()}
                {if !$driver}
                    {_unknown_video}
                {else}
                    {$driver->getEmbed()|noescape}
                {/if}
            {/if}
        </div>
        <div class="video_info">
            <div class='video_info_title'>
                {$video->getName()}
            </div>
            <div class="video_info_actions">
                {if isset($thisUser)}
                    {var $liked = $video->hasLikeFrom($thisUser)}
                    {var $likesCount = $video->getLikesCount()}
                    <a href="/video{$video->getPrettyId()}/like?hash={rawurlencode($csrfToken)}" class="video_like_button button video_like_wrap" data-liked="{(int) $liked}" data-likes="{$likesCount}" data-id="{$video->getPrettyId()}" data-type='video'>
                        <i class="heart video_like_icon _icon" id="{if $liked}liked{/if}"></i>
                        <span class="video_like_link _link">{tr('mobile_like')}</span>
                        <span class="likeCnt video_like_count _count">{if $likesCount > 0}{$likesCount}{/if}</span>
                    </a>
                    <a onclick="javascript:repost({$video->getPrettyId()}, 'video')" class="video_share_button button button_light _share_wrap">
                        <i class="repost-icon video_share_icon _icon"></i>
                        <span class="video_share_link _link">{tr('share')}</span>
                    </a>
                    <a href="/video{$video->getPrettyId()}/edit" class="video_edit_button button button_light _edit_wrap">
                        <i class="edit-icon video_edit_icon _icon"></i>
                        <span class="video_edit_link _link">{tr('edit')}</span>
                    </a>
                    <span class="video_info_more_actions">
                        <div class="button button_light _more_wrap">{_show_more}</div>
                        <div id="videoMoreActionsTooltip" class="tippy-menu">
                            <a n:if="$thisUser->getId() === $video->getOwner()->getId()" href="/video{$video->getPrettyId()}/remove">
                                <i class="delete-icon video_delete_icon _icon"></i>
                                <span class="video_delete_link _link">{tr('delete')}</span>
                            </a>
                            <a n:if="$canReport" href="javascript:reportVideo({$video->getId()})">
                                <i class="report-icon video_report_icon _icon"></i>
                                <span class="video_report_link _link">{tr('report')}</span>
                            </a>
                            <a n:if="$video->getType() == 0" href="{$video->getURL()}" download="">
                                <i class="download-icon video_download_icon _icon"></i>
                                <span class="video_download_link _link">{tr('download_video')}</span>
                            </a>
                        </div>
                    </span>
                {/if}
            </div>
            <div class="video_info_author clear_fix">
                <a href="/{$video->getOwner()->getId()}" class="video_info_author_image">
                    <img class="post-avatar" src="{$video->getOwner()->getAvatarUrl('miniscule')}" />
                </a>
                <div class="video_info_author_info">
                    <div class="video_info_author_name">
                        <a href="/{$video->getOwner()->getId()}">{$video->getOwner()->getCanonicalName()}</a>
                    </div>
                    <div class="video_info_author_date">
                        {$video->getPublicationTime()}
                    </div>
                </div>
            </div>
            <div class="video_info_description" n:if="$video->getDescription()">
                {if strlen($video->getDescription()) > 500}
                    <span class="truncated_text">{$video->getDescription()|truncate:500|noescape}</span> 
                    <span class="full_text hidden">{$video->getDescription()|noescape}</span>
                    <a href="javascript:void(0)" class="expand_note" onclick="toggleLongText(this)">{_show_more}</a>
                {else}
                    {$video->getDescription()|noescape}
                {/if}
            </div>
        </div>
        <div class="clear_fix video_comments">
            {include "../components/comments.xml",
                    comments => $comments,
                    count => $cCount,
                    page => $cPage,
                    model => "videos",
                    parent => $video}
        </div>
    </div>
{/block}
