{ifset $thisUser}
    {if $thisUser->getNsfwTolerance() < 2}
        {css "css/nsfw-posts.css"}
    {/if}

    {if $theme !== NULL}
        {if $theme->inheritDefault()}
            {css "css/main.css"}
            {css "css/bsdn.css"}
            {css "css/dialog.css"}
            <link rel="stylesheet" href="/themepack/{$theme->getId()}/{$theme->getVersion()}/resource/css/notifications.css" />
            {css "css/avatar-edit.css"}
            {css "css/audios.css"}

            {if $isXmas}
                {css "css/xmas.css"}
            {/if}
        {/if}

        <link rel="stylesheet" href="/themepack/{$theme->getId()}/{$theme->getVersion()}/stylesheet/styles.css" />
        <link rel="stylesheet" href="/themepack/{$theme->getId()}/{$theme->getVersion()}/resource/css/profile.css" />
        <link rel="stylesheet" href="/themepack/{$theme->getId()}/{$theme->getVersion()}/resource/css/settings.css" />
        <link rel="stylesheet" href="/themepack/{$theme->getId()}/{$theme->getVersion()}/resource/css/modals.css" />

        {if $isXmas}
            <link rel="stylesheet" href="/themepack/{$theme->getId()}/{$theme->getVersion()}/resource/xmas.css" />
        {/if}
    {else}

        {css "css/main.css"}
        {css "css/bsdn.css"}
        {css "css/dialog.css"}
        {css "css/notifications.css"}
        {css "css/avatar-edit.css"}
        {css "css/audios.css"}
        
        {if $isXmas}
            {css "css/xmas.css"}
        {/if}
    {/if}

    {if $thisUser->getStyleAvatar() == 1}
        {css "css/avatar.1.css"}
    {/if}

    {if $thisUser->getStyleAvatar() == 2}
        {css "css/avatar.2.css"}
    {/if}
{else}
    {css "css/main.css"}
    {css "css/bsdn.css"}
    {css "css/dialog.css"}
    {css "css/notifications.css"}
    {css "css/nsfw-posts.css"}
    {css "css/audios.css"}
    {if $theme !== NULL}
        <link rel="stylesheet" href="/themepack/{$theme->getId()}/{$theme->getVersion()}/stylesheet/styles.css" />
        <link rel="stylesheet" href="/themepack/{$theme->getId()}/{$theme->getVersion()}/resource/css/profile.css" />
        <link rel="stylesheet" href="/themepack/{$theme->getId()}/{$theme->getVersion()}/resource/css/settings.css" />
        <link rel="stylesheet" href="/themepack/{$theme->getId()}/{$theme->getVersion()}/resource/css/modals.css" />
    {/if}
{/ifset}
