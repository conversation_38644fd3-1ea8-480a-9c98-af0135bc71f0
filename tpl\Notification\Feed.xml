{extends "../@layout.xml"}
{var $sorting = false}

{block title}
    {_feedback}
{/block}

{block content}
{include "../components/page_crumb_header.xml", crumbs => [
    ['href' => $thisUser->getURL(), 'title' => $thisUser->getCanonicalName()],
    ['title' => tr("feedback")]
]}
<div class="page_block notifications">
    {include "../components/page_tabs_header.xml", white => true, tabs => [
        [
            'url' => '/notifications?act=new',
            'title' => 'unread',
            'active' => $mode === 'new'
        ],
        [
            'url' => '/notifications?act=archived',
            'title' => 'archive',
            'active' => $mode === 'archived'
        ]
    ]}
    {var $data = is_array($iterator) ? $iterator : iterator_to_array($iterator)}
    {if sizeof($data) > 0}
        <div n:class="$mode !== 'new' ? scroll_container">
            <div class="feedback_row scroll_node" n:foreach="$data as $dat">
                {var $sxModel = $dat->getModel(1)}
                {if !(method_exists($sxModel, "getURL") && method_exists($sxModel, "getAvatarUrl"))}
                    {var $sxModel = $dat->getModel(0)}
                {/if}
                <a href="{$sxModel->getURL()}" class="feedback_image" n:if="method_exists($sxModel, 'getURL') && method_exists($sxModel, 'getAvatarUrl')">
                    <img src="{$sxModel->getAvatarUrl('miniscule')}" width=50 />
                </a>
                <div class="feedback_content">
                    {include $dat->getTemplatePath(), notification => $dat}
                </div>
            </div>
        </div>
        {include "../components/paginator.xml", conf => (object) [
            "page"     => $page,
            "count"    => $count,
            "amount"   => sizeof($data),
            "perPage"  => $perPage ?? OPENVK_DEFAULT_PER_PAGE,
            "atBottom" => true,
        ]}
    {else}
        {ifset customErrorMessage}
            {include customErrorMessage}
        {else}
            {include "../components/nothing.xml"}
        {/ifset}
    {/if}
</div>
{/block}
